import { <PERSON><PERSON>, HeroProvider, NavigationBar, SocialIcon } from '@venture-vibe/components/shared'
import { InstagramIcon, LinkedinIcon, TiktokIcon, YoutubeIcon } from '@venture-vibe/icons'
import Image from 'next/image'
import { redirect } from 'next/navigation'
import { getUser } from '../../../api/users'
import { footerLinks } from '../../(client)/test-data'

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  const user = await getUser()

  if (!user) {
    redirect('/admin/login')
  }

  return (
    <>
      <HeroProvider>
        <NavigationBar
          logo={
            <Image
              src='/logo-group.svg'
              alt='Venture Vibe'
              className='max-w-[160px] max-h-[52px] ml-5'
              width={160}
              height={32}
            />
          }
          links={[]}
        />
        {children}
      </HeroProvider>
      {children}
      <Footer
        className='mt-auto'
        iconUrl='/logo-group.svg'
        title='Bültenimize Abone Olun'
        description='<PERSON><PERSON>, yeni ba<PERSON> kurun ve topluluğumuzun bir parçası olun.'
        menuItems={footerLinks}
        bottomLeftText={
          <>
            VENTUREVIBE TECHNOLOGY LTD
            <br />
            141c High Road, Loughton, England, IG10 4LT
          </>
        }
        bottomRightText={
          <>
            © 2025 Yasal
            <br />
            Venture Vibe Tüm Hakları Saklıdır.
          </>
        }
        buttonText='Abone ol'
        socialIcons={
          <div className='flex flex-row gap-8'>
            <SocialIcon
              color='primary'
              className='hover:!text-[#E4405F]'
              icon={<InstagramIcon />}
              href='https://www.instagram.com/helloventurevibe/'
            />
            <SocialIcon
              color='primary'
              className='hover:text-red'
              icon={<YoutubeIcon />}
              href='https://www.youtube.com/@HelloVentureVibe'
            />
            <SocialIcon
              color='primary'
              className='hover:text-dark-blue'
              icon={<TiktokIcon />}
              href='https://www.tiktok.com/@helloventurevibe'
            />
            <SocialIcon
              color='primary'
              icon={<LinkedinIcon />}
              href='https://www.linkedin.com/company/the-venture-vibe/'
            />
          </div>
        }
        socialTitle='Bizi Takip Edin'
      />
    </>
  )
}
