'use client'

import type { CheckedState } from '@radix-ui/react-checkbox'
import { Button, Checkbox, Input, Text, TextArea, Title } from '@venture-vibe/components/shared'
import { useState } from 'react'

export default function CreateAdvertPage() {
  const [formData, setFormData] = useState({
    company: '',
    location: '',
    title: '',
    description: '',
    tags: '',
    type: 'freelance',
    applyUrl: '',
    content: '',
    mail: '',
    phone: '',
    address: '',
  })

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleTypeChange = (type: 'freelance' | 'startup' | 'project') => (checked: CheckedState) => {
    if (checked === true) {
      setFormData(prev => ({
        ...prev,
        type,
      }))
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    // const dataToSubmit = {
    //   ...formData,
    //   tags: formData.tags.split(',').map(tag => tag.trim()),
    //   id: new Date().toISOString(),
    //   slug: formData.title.toLowerCase().replace(/\s+/g, '-'),
    // }
  }

  return (
    <section className='w-full desktop:py-64 tablet:py-64 max-tablet:py-64'>
      <div className='w-11/12 lg:w-2/4 mx-auto px-10 desktop:max-w-[1280px] tablet:max-w-[790px]'>
        <div className='bg-white w-full p-10 rounded-max border border-platinum-stroke shadow-sm'>
          <Title as='h1' className='text-4xl font-bold mb-4 text-center'>
            Yeni İş İlanı Oluştur
          </Title>
          <Text className='text-lg text-dark-gray mb-8 text-center'>
            Yeni bir iş ilanı yayınlamak için aşağıdaki bilgileri doldurun.
          </Text>
          <form onSubmit={handleSubmit} className='space-y-8 w-full'>
            <div className='grid grid-cols-1 w-full md:grid-cols-2 gap-8'>
              <div className='flex w-full flex-col gap-2 md:col-span-2'>
                <Text as='label'>Şirket</Text>
                <Input
                  name='company'
                  value={formData.company}
                  onChange={handleChange}
                  placeholder='Venture Vibe'
                  required
                  className='w-full'
                />
              </div>
              <div className='flex flex-col gap-2 md:col-span-2'>
                <Text as='label'>Konum</Text>
                <Input
                  name='location'
                  value={formData.location}
                  onChange={handleChange}
                  placeholder='İstanbul, TR'
                  required
                  className='w-full'
                />
              </div>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-8 md:col-span-2'>
                <div className='flex flex-col gap-2'>
                  <Text as='label'>İş Unvanı</Text>
                  <Input
                    name='title'
                    value={formData.title}
                    onChange={handleChange}
                    placeholder='Backend Developer'
                    required
                    className='w-full'
                  />
                </div>
                <div className='flex flex-col gap-2'>
                  <Text as='label'>Etiketler</Text>
                  <Input
                    name='tags'
                    value={formData.tags}
                    onChange={handleChange}
                    placeholder='#Istanbul, #Backend, #React'
                    required
                    className='w-full'
                  />
                </div>
              </div>
              <div className='flex flex-col gap-2 md:col-span-2'>
                <Text as='label'>Kısa Açıklama</Text>
                <TextArea
                  name='description'
                  value={formData.description}
                  onChange={handleChange}
                  placeholder='İşin kısa bir özeti.'
                  required
                  className='h-24 rounded-xl w-full'
                />
              </div>
              <div className='flex flex-col gap-2 md:col-span-2'>
                <Text as='label'>İş İçeriği (Markdown)</Text>
                <TextArea
                  name='content'
                  value={formData.content}
                  onChange={handleChange}
                  placeholder='Markdown formatında detaylı iş açıklaması.'
                  required
                  className='h-64 rounded-xl'
                />
              </div>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-8 md:col-span-2'>
                <div className='flex flex-col gap-2'>
                  <Text as='label'>Başvuru URL'si</Text>
                  <Input
                    name='applyUrl'
                    type='url'
                    value={formData.applyUrl}
                    onChange={handleChange}
                    placeholder='https://example.com/basvur'
                    required
                    className='w-full'
                  />
                </div>
                <div className='flex flex-col gap-2'>
                  <Text as='label'>Adres</Text>
                  <Input
                    name='address'
                    value={formData.address}
                    onChange={handleChange}
                    placeholder='123 Ana Cad, İstanbul, Türkiye'
                    className='w-full'
                  />
                </div>
              </div>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-8 md:col-span-2'>
                <div className='flex flex-col gap-2'>
                  <Text as='label'>İletişim Telefonu</Text>
                  <Input
                    name='phone'
                    type='tel'
                    value={formData.phone}
                    onChange={handleChange}
                    placeholder='(*************'
                    className='w-full'
                  />
                </div>
                <div className='flex flex-col gap-2'>
                  <Text as='label'>İletişim E-postası</Text>
                  <Input
                    name='mail'
                    type='email'
                    value={formData.mail}
                    onChange={handleChange}
                    placeholder='örn: <EMAIL>'
                    required
                    className='w-full'
                  />
                </div>
              </div>
              <div className='flex flex-col gap-2 md:col-span-2'>
                <Text as='label' className='font-medium mb-2'>
                  İş Türü
                </Text>
                <div className='flex flex-row gap-4'>
                  <Checkbox
                    id={String('freelance')}
                    label='Freelance'
                    checked={formData.type === 'freelance'}
                    onCheckedChange={handleTypeChange('freelance')}
                  />
                  <Checkbox
                    id={String('startup')}
                    label='Startup'
                    checked={formData.type === 'startup'}
                    onCheckedChange={handleTypeChange('startup')}
                  />
                  <Checkbox
                    id={String('project')}
                    label='Proje'
                    checked={formData.type === 'project'}
                    onCheckedChange={handleTypeChange('project')}
                  />
                </div>
              </div>
            </div>
            <div className='flex justify-end'>
              <Button type='submit' color='primary' size='lg' className='w-full md:w-auto'>
                İlanı Yayınla
              </Button>
            </div>
          </form>
        </div>
      </div>
    </section>
  )
}
