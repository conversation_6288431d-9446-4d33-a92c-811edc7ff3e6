'use client'

import type { CheckedState } from '@radix-ui/react-checkbox'
import {
  Appear,
  Badge,
  Button,
  Checkbox,
  GlobalSearchProvider,
  GlobalSearchTrigger,
  GridFlexContainer,
  ProjectCard,
  Text,
} from '@venture-vibe/components/shared'
import { useMemo, useState } from 'react'
import { cardJobsData } from '@/app/test-data'

export default function JobsPage() {
  const [selectedTag, setSelectedTag] = useState<string>('all')
  const [selectedTypes, setSelectedTypes] = useState<string[]>([])

  const allTags = useMemo(() => {
    const tags = cardJobsData.flatMap(job => job.tags)
    return Array.from(new Set(tags))
  }, [])

  const handleTagClick = (tag: string) => {
    setSelectedTag(tag)
  }

  const handleTypeChange = (checked: CheckedState, type: string) => {
    setSelectedTypes(prev => {
      if (checked === true) {
        return [...prev, type]
      }
      return prev.filter(t => t !== type)
    })
  }

  const filteredJobs = useMemo(() => {
    let jobs = cardJobsData

    if (selectedTag !== 'all') {
      jobs = jobs.filter(job => job.tags.includes(selectedTag))
    }

    if (selectedTypes.length > 0) {
      jobs = jobs.filter(job => selectedTypes.includes(job.type))
    }

    return jobs
  }, [selectedTag, selectedTypes])

  const searchLinks = useMemo(() => {
    const slugLinks = cardJobsData.map(job => ({
      href: `/jobs/${job.slug}`,
      title: job.title,
      category: job.company,
    }))
    return [...slugLinks]
  }, [])

  return (
    <section className='w-full bg-bg pb-50'>
      <div className='flex flex-col px-10 mx-auto max-w-[82.5rem] desktop:gap-28 tablet:gap-20 max-tablet:gap-15'>
        <Appear className='w-full'>
          <GridFlexContainer layout='flex' direction='column' className='w-full md:flex-row gap-10 mt-60'>
            {/* Right Side: Tag Filter */}
            <div className='md:w-1/4 w-full gap-10 flex flex-col'>
              <div className='sticky top-60 bg-white p-5 rounded-2xl'>
                <Text as='p' size='md' className='mb-10' thickness='bold'>
                  Kategoriler
                </Text>
                <GridFlexContainer layout='flex' direction='row' wrap='wrap' gap='sm' className=''>
                  <Button
                    key='all'
                    size='sm'
                    color={selectedTag === 'all' ? 'primary' : 'outlineBlack'}
                    onClick={() => handleTagClick('all')}
                    className='w-auto border-platinum-stroke'>
                    <Text as='p' size='md'>
                      Tümü
                    </Text>
                  </Button>
                  {allTags.map(tag => (
                    <Button
                      key={tag}
                      size='sm'
                      color={selectedTag === tag ? 'primary' : 'outlineBlack'}
                      onClick={() => handleTagClick(tag)}
                      className='w-auto border-platinum-stroke'>
                      <Text as='p' size='md'>
                        {tag}
                      </Text>
                    </Button>
                  ))}
                </GridFlexContainer>
              </div>
              <div className='sticky top-250 flex flex-col p-5 gap-4 bg-white rounded-2xl'>
                <Text as='p' size='md' className='mb-4' thickness='bold'>
                  İş Tipi
                </Text>
                <div className='flex flex-row gap-4'>
                  <Checkbox
                    id={String('freelance')}
                    label='Freelance'
                    checked={selectedTypes.includes('freelance')}
                    onCheckedChange={checked => handleTypeChange(checked, 'freelance')}
                  />
                  <Checkbox
                    id={String('startup')}
                    label='Startup'
                    checked={selectedTypes.includes('startup')}
                    onCheckedChange={checked => handleTypeChange(checked, 'startup')}
                  />
                  <Checkbox
                    id={String('project')}
                    label='Proje'
                    checked={selectedTypes.includes('project')}
                    onCheckedChange={checked => handleTypeChange(checked, 'project')}
                  />
                </div>
              </div>
            </div>
            {/* Left Side: News Grid */}
            <div className='md:w-3/4 w-full'>
              <div className='flex flex-row gap-4 justify-between'>
                <GlobalSearchProvider links={searchLinks}>
                  <GlobalSearchTrigger
                    className='w-full h-32 rounded-xl mb-10'
                    placeholder='İş ilanı veya şirket ara'
                  />
                  <Button
                    size='lg'
                    className='whitespace-nowrap h-31 text-sm !rounded-xl'
                    onClick={() => {
                      window.location.href = '/jobs/advert'
                    }}>
                    İş İlanı Yayınla
                  </Button>
                </GlobalSearchProvider>
              </div>

              <GridFlexContainer layout='grid' columns={3} gap='lg'>
                {filteredJobs.map(job => (
                  <ProjectCard
                    key={job.id}
                    link={`/jobs/${job.slug}`}
                    tags={job.tags}
                    color='light-spring'
                    title={job.title}
                    description={job.description}
                    buttonText='İlanı Görüntüle'
                    badge={
                      <div className='flex flex-row gap-4'>
                        <Badge size='xs' variant='secondary-accent' text={job.company} />
                        <Badge size='xs' variant='quaternary-border' text={job.location} />
                      </div>
                    }
                  />
                ))}
              </GridFlexContainer>
            </div>
          </GridFlexContainer>
        </Appear>
      </div>
    </section>
  )
}
