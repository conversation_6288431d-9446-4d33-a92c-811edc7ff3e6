import '../globals.css'
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>aq,
  <PERSON>er,
  GridFlexContainer,
  HeroProvider,
  NavigationBar,
  SocialIcon,
  Text,
  Title,
} from '@venture-vibe/components/shared'
import { InstagramIcon, LinkedinIcon, TiktokIcon, YoutubeIcon } from '@venture-vibe/icons'
import Image from 'next/image'
import { faqData, footerLinks, navbarLinks } from './test-data'

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang='en'>
      <head>
        <title>Venture Vibe</title>
        <meta name='description' content='Generated by create next app' />
        <meta name='viewport' content='width=device-width, initial-scale=1' />
        <link rel='icon' href='/favicon.ico' />
      </head>
      <body className='antialiased'>
        <HeroProvider>
          <NavigationBar
            logo={
              <Image
                src='/logo-group.svg'
                alt='Venture Vibe'
                className='max-w-[160px] max-h-[52px] ml-5'
                width={160}
                height={32}
              />
            }
            links={navbarLinks}
            button-info={{ text: 'Katıl', href: 'a' }}
            second-button-info={{ text: 'İçerikleri Keşfet', href: 'b' }}
          />
          {children}
        </HeroProvider>
        <section className='w-full min-h-304 flex justify-center'>
          <GridFlexContainer
            layout='flex'
            direction='column'
            align='center'
            justify='center'
            className='w-full mx-auto px-10 desktop:max-w-[82.5rem] space-y-10 md:space-y-0 py-50 tablet:flex-row'>
            <Appear direction='right' className='w-full h-full'>
              <GridFlexContainer
                layout='flex'
                direction='column'
                align='start'
                justify='start'
                className='w-full h-full'>
                <Title
                  as='h4'
                  size={null}
                  className='text-[30px] leading-[48px] sm:text-[35px] tablet:text-[40px] tablet:leading-[41px]'>
                  Sıkça Sorulan Sorular
                </Title>
                <Text className='mr-60'>
                  Tüm premium içerik ve analizlerimize sınırsız erişim sunan üyeliğimizle bilgiye olan açlığınızı
                  giderin ve potansiyelinizi en üst düzeye çıkarın.
                </Text>
              </GridFlexContainer>
            </Appear>
            <Appear className='w-full' options={{ threshold: 0.6 }}>
              <GridFlexContainer layout='flex' direction='column' align='center' justify='center' className='w-full'>
                <Faq items={faqData} type='single' collapsible={true} color='light' className='w-full max-w-4xl' />
              </GridFlexContainer>
            </Appear>
          </GridFlexContainer>
        </section>
        <Footer
          iconUrl='/logo-group.svg'
          title='Bültenimize Abone Olun'
          description='Profil oluşturun, yeni bağlantılar kurun ve topluluğumuzun bir parçası olun.'
          menuItems={footerLinks}
          bottomLeftText={
            <>
              VENTUREVIBE TECHNOLOGY LTD
              <br />
              141c High Road, Loughton, England, IG10 4LT
            </>
          }
          bottomRightText={
            <>
              © 2025 Yasal
              <br />
              Venture Vibe Tüm Hakları Saklıdır.
            </>
          }
          buttonText='Abone ol'
          socialIcons={
            <div className='flex flex-row gap-8'>
              <SocialIcon
                color='primary'
                className='hover:!text-[#E4405F]'
                icon={<InstagramIcon />}
                href='https://www.instagram.com/helloventurevibe/'
              />
              <SocialIcon
                color='primary'
                className='hover:text-red'
                icon={<YoutubeIcon />}
                href='https://www.youtube.com/@HelloVentureVibe'
              />
              <SocialIcon
                color='primary'
                className='hover:text-dark-blue'
                icon={<TiktokIcon />}
                href='https://www.tiktok.com/@helloventurevibe'
              />
              <SocialIcon
                color='primary'
                icon={<LinkedinIcon />}
                href='https://www.linkedin.com/company/the-venture-vibe/'
              />
            </div>
          }
          socialTitle='Bizi Takip Edin'
        />
      </body>
    </html>
  )
}
