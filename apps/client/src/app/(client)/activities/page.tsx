import {
  Appear,
  BackgroundS<PERSON><PERSON>,
  <PERSON>ge,
  <PERSON><PERSON>,
  Hero,
  Noise,
  Text,
  Timeline,
  Title,
} from '@venture-vibe/components/shared'
import { timelineItems } from '../test-data'

export default async function Activities(props: { searchParams: Promise<{ tag?: string; location?: string }> }) {
  const selectedTag = (await props.searchParams).tag || 'all'
  const selectedLocation = (await props.searchParams).location || 'all'

  const allTags = Array.from(new Set(timelineItems.flatMap(item => item.tags)))
  const allLocations = Array.from(new Set(timelineItems.map(item => item.location)))

  const filteredItems = timelineItems.filter(item => {
    const tagMatch = selectedTag === 'all' || item.tags.includes(selectedTag)
    const locationMatch = selectedLocation === 'all' || item.location === selectedLocation
    return tagMatch && locationMatch
  })

  return (
    <>
      <Hero as='section' className='w-full h-max'>
        <BackgroundSection
          as='div'
          padding={null}
          layers={[
            { type: 'color', color: '#C5F910' },
            {
              type: 'node',
              node: (
                <div className='flex justify-center items-center absolute inset-0 bg-cover bg-center h-full'>
                  {/** biome-ignore lint/performance/noImgElement: Reduntant */}
                  <img
                    src='/grid.svg'
                    alt='a'
                    width={520}
                    height={520}
                    className='shrink-0 desktop:min-w-[1340px] desktop:min-h-[900px] tablet:min-w-[1024px] tablet:min-h-[768px] max-tablet:min-w-[768px] max-tablet:min-h-[660px]'
                  />
                </div>
              ),
            },
            {
              type: 'node',
              node: (
                <div className='w-full h-full'>
                  <Noise className='w-full h-full' />
                </div>
              ),
            },
          ]}
          className='pb-[80px] desktop:h-[528px] desktop:pt-[192px] desktop:pb-[124px] tablet:h-[430px] tablet:pt-[160px] max-tablet:h-[448px] max-tablet:pt-[140px]'>
          <Appear className='flex flex-col gap-8 w-full justify-center items-center px-10'>
            <Badge text='Etkinlikler' className='w-fit tablet:p-10 max-tablet:p-8' />
            <div className='flex flex-col gap-12 justify-center items-center'>
              <Title
                as='h2'
                size={null}
                className='font-display desktop:text-[56px] tablet:text-[46px] max-tablet:text-[40px]'>
                En Yeni Etkinlikler
              </Title>
              <Text size={null} className='text-center tablet:max-w-[680px] tablet:text-[18px] max-tablet:text-[16px]'>
                Girişimcilik dünyasındaki en güncel etkinlikleri keşfedin. İlham verici buluşmalar, yenilikçi fikirler
                ve iş birliği fırsatlarıyla ekosistemin bir parçası olun.
              </Text>
            </div>
          </Appear>
        </BackgroundSection>
      </Hero>
      <section className='flex flex-col justify-center items-center px-10 w-full desktop:py-[96px] tablet:py-[80px] max-tablet:py-[60px]'>
        <div className='flex flex-col gap-60 max-tablet:gap-40 justify-between items-center w-full desktop:max-w-[1000px] tablet:max-w-[790px]'>
          <Appear late className='w-full flex justify-center items-center'>
            <div className='flex flex-row items-center -mt-20 justify-center gap-4 w-full lg:w-1/2 border border-platinum-stroke p-4 rounded-lg'>
              <form
                method='GET'
                action='/activities'
                className='flex flex-row items-center justify-stretch gap-4 w-full max-w-md'>
                <div className='flex gap-4 w-full'>
                  <div className='relative w-full'>
                    <select
                      name='tag'
                      defaultValue={selectedTag}
                      className='w-full px-2 py-4 text-gray text-xs h-full border border-platinum-stroke rounded-lg bg-transparent shadow-sm outline-none appearance-none'>
                      <option value='all'>Tüm Kategoriler</option>
                      {allTags.map(tag => (
                        <option key={tag} value={tag}>
                          {tag}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div className='relative w-full'>
                    <select
                      name='location'
                      defaultValue={selectedLocation}
                      className='w-full px-2 py-4 text-gray text-xs h-full border border-platinum-stroke rounded-lg bg-transparent shadow-sm outline-none appearance-none'>
                      <option value='all'>Tüm Lokasyonlar</option>
                      {allLocations.map(location => (
                        <option key={location} value={location}>
                          {location}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
                <Button
                  type='submit'
                  color='outlineBlack'
                  size='lg'
                  className='h-18 w-32 text-xs border-platinum-stroke border !rounded-lg'>
                  Filtrele
                </Button>
              </form>
            </div>
          </Appear>
          <Timeline items={filteredItems} />
        </div>
      </section>
    </>
  )
}
