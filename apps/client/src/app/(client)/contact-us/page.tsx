import {
  Appear,
  BackgroundSection,
  <PERSON>ge,
  <PERSON><PERSON>,
  Hero,
  Input,
  Link,
  Noise,
  Text,
  TextArea,
  Title,
} from '@venture-vibe/components/shared'
import { HandsetIcon, Mail, MapPin } from '@venture-vibe/icons'
import { clsx } from '@venture-vibe/utils'

export default function ContactUs() {
  return (
    <>
      <Hero as='section' className='w-full h-max'>
        <BackgroundSection
          padding={null}
          layers={[
            { type: 'color', color: '#C5F910' },
            {
              type: 'node',
              node: (
                <div className='flex justify-center items-center absolute inset-0 bg-cover bg-center h-full'>
                  {/** biome-ignore lint/performance/noImgElement: Reduntant */}
                  <img
                    src='/grid.svg'
                    alt='a'
                    width={520}
                    height={520}
                    className='shrink-0 desktop:min-w-[1340px] desktop:min-h-[900px] tablet:min-w-[1024px] tablet:min-h-[768px] max-tablet:min-w-[768px] max-tablet:min-h-[660px]'
                  />
                </div>
              ),
            },
            {
              type: 'node',
              node: (
                <div className='w-full h-full'>
                  <Noise className='w-full h-full' />
                </div>
              ),
            },
          ]}
          className='pb-[80px] desktop:h-[528px] desktop:pt-[192px] desktop:pb-[124px] tablet:h-[430px] tablet:pt-[160px] max-tablet:h-[448px] max-tablet:pt-[140px]'>
          <Appear className='flex flex-col gap-8 w-full justify-center items-center px-10'>
            <Badge text='Bize ulaş' className='w-fit tablet:p-10 max-tablet:p-8' />
            <div className='flex flex-col gap-12 justify-center items-center'>
              <Title
                as='h2'
                size={null}
                className='font-display desktop:text-[56px] tablet:text-[46px] max-tablet:text-[40px]'>
                Senin İçin Burdayız
              </Title>
              <Text size={null} className='text-center tablet:max-w-[578px] tablet:text-[18px] max-tablet:text-[16px]'>
                Bir sorunuz veya yardıma mı ihtiyacınız var?
                <br />
                Özel destek ekibimizle iletişime geçin!
              </Text>
            </div>
          </Appear>
        </BackgroundSection>
      </Hero>
      <section className='flex flex-row justify-center items-center px-10 w-full desktop:py-[96px] tablet:py-[80px] max-tablet:py-[60px]'>
        <div className='flex max-tablet:flex-col max-tablet:gap-15 justify-between items-between w-full desktop:max-w-[1280px] tablet:max-w-[790px]'>
          <Appear direction='right' className='flex flex-col desktop:gap-60 tablet:gap-30 max-tablet:gap-20'>
            <div className='flex flex-col gap-8'>
              <Title as='h4' size={null} className='desktop:text-[40px] tablet:text-[34px] max-tablet:text-[30px]'>
                Bizimle İletişime Geç
              </Title>
              <Text size={null} className='tablet:text-[18px] max-tablet:text-[16px]'>
                Ulaşmaktan çekinmeyin - İletişimde bulunmayı çok isteriz
              </Text>
            </div>
            <div className='w-full h-full max-desktop:h-min mx-auto'>
              <div className='flex max-desktop:flex-col gap-12 justify-between h-full font-display text-[20px] leading-[20px] text-dark-gray'>
                <div className='flex flex-col max-desktop:gap-12 justify-between'>
                  <div className='flex flex-col gap-4'>
                    <div className='flex gap-5'>
                      <Mail width={24} height={24} />
                      <Title as='h6' size={null}>
                        Email Adresimiz
                      </Title>
                    </div>
                    <Text as='span' size='lg'>
                      <EMAIL>
                    </Text>
                  </div>
                  {/** I said, "God help me!" */}
                  <div className='relative desktop:translate-y-1/1 flex flex-col gap-4'>
                    <div className='flex gap-5'>
                      <MapPin width={24} height={24} />
                      <Title as='h6' size={null}>
                        Ofisimizi Ziyaret Et
                      </Title>
                    </div>
                    <Text as='span' size='lg'>
                      VENTUREVIBE TECHNOLOGY LTD
                      <br />
                      141c High Road,Loughton, England, IG10 4LT
                    </Text>
                  </div>
                </div>
                <div className='flex flex-col max-desktop:gap-12 justify-between'>
                  <div className='flex flex-col gap-4'>
                    <div className='flex gap-5'>
                      <HandsetIcon width={24} height={24} strokeWidth={3} />
                      <Title as='h6' size={null}>
                        Telefon Numaramız
                      </Title>
                    </div>
                    <Text as='span' size='lg'>
                      +44 7552 487007
                    </Text>
                  </div>
                  {/** And again... */}
                  <div className='relative desktop:translate-y-1/1 flex flex-col gap-4'>
                    <div className='flex gap-5'>
                      <Mail width={24} height={24} />
                      <Title as='h6' size={null}>
                        Toplantı Planlayın
                      </Title>
                    </div>
                    <Link href='schedule'>
                      <Text as='span' size='lg' decoration='underline'>
                        Hemen planlama yapın
                      </Text>
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </Appear>
          <Appear
            direction='left'
            className={clsx(
              'flex',
              'flex-col',
              'items-center',
              'justify-center',
              'desktop:max-w-[49%]',
              'tablet:max-w-[53%]',
              'desktop:p-16',
              'w-full',
              'gap-8',
              'bg-white',
              'border border-platinum-stroke',
              'p-14',
              'rounded-[32px]',
            )}
            late>
            <div className={clsx('flex flex-col gap-8 desktop:flex-row desktop:gap-20', 'w-full')}>
              <div className={clsx('flex flex-col gap-4', 'w-full')}>
                <Text as='label'>Ad / Soyad</Text>
                <Input placeholder='Tam isminiz' size='lg' />
              </div>
              <div className={clsx('flex flex-col gap-4', 'w-full')}>
                <Text as='label'>Email Adresi</Text>
                <Input placeholder='Email Adresiniz' size='lg' />
              </div>
            </div>
            <div className={clsx('flex flex-col gap-4', 'w-full')}>
              <Text as='label'>Başlık</Text>
              <Input placeholder='Başlık' size='lg' />
            </div>
            <div className={clsx('flex flex-col gap-4', 'w-full')}>
              <Text as='label'>Mesaj</Text>
              <TextArea
                isResizable={false}
                placeholder='Birşeyler yazınız'
                className={clsx(
                  'h-32',
                  'border border-light-gray rounded-lg bg-white text-sm focus:ring-light-gray focus:border-light-gray',
                )}
              />
              <Button color='secondary' size='lg' className='w-80 mt-10 text-md cursor-pointer'>
                Mesajı Gönder
              </Button>
            </div>
          </Appear>
        </div>
      </section>
      <Appear as='section' className='flex w-full justify-center items-center' late>
        <iframe
          title='Map'
          loading='lazy'
          allowFullScreen
          referrerPolicy='no-referrer-when-downgrade'
          src='https://www.google.com/maps/embed/v1/place?key=key&q=Space+Needle,Seattle+WA'
          className='w-full mx-10 desktop:h-[572px] desktop:max-w-[1280px] tablet:h-[378px] tablet:max-w-[790px] max-tablet:h-[240px]'
        />
      </Appear>
    </>
  )
}
