'use client'

import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  GlobalSearchProvider,
  GlobalSearchTrigger,
  GridFlexContainer,
  Text,
} from '@venture-vibe/components/shared'
import { useMemo, useState } from 'react'
import { cardBlogsData } from '@/app/test-data'

export default function NewsPage() {
  const [selectedTag, setSelectedTag] = useState<string>('all')

  const allTags = useMemo(() => {
    const tags = cardBlogsData.flatMap(blog => blog.tags)
    return Array.from(new Set(tags))
  }, [])

  const handleTagClick = (tag: string) => {
    setSelectedTag(tag)
  }

  const filteredBlogs = useMemo(() => {
    if (selectedTag === 'all') {
      return cardBlogsData
    }
    return cardBlogsData.filter(blog => blog.tags.includes(selectedTag))
  }, [selectedTag])

  const searchLinks = useMemo(() => {
    const slugLinks = cardBlogsData.map(blog => ({
      href: `/blogs/${blog.slug}`,
      title: blog.title,
    }))
    return [...slugLinks]
  }, [])

  return (
    <section className='w-full bg-bg pb-50'>
      <div className='flex flex-col px-10 mx-auto max-w-[82.5rem] desktop:gap-28 tablet:gap-20 max-tablet:gap-15'>
        <div className='w-full'>
          <GridFlexContainer layout='flex' direction='column' className='w-full md:flex-row gap-10 mt-60'>
            {/* Right Side: Tag Filter */}
            <Appear direction='right' className='md:w-1/4 w-full'>
              <div className='sticky top-60 bg-white p-5 rounded-2xl'>
                <Text as='p' size='md' className='mb-10' thickness='bold'>
                  Kategoriler
                </Text>
                <GridFlexContainer layout='flex' direction='row' wrap='wrap' gap='sm' className=''>
                  <Button
                    key='all'
                    size='sm'
                    color={selectedTag === 'all' ? 'primary' : 'outlineBlack'}
                    onClick={() => handleTagClick('all')}
                    className='w-auto border-platinum-stroke'>
                    <Text as='p' size='md'>
                      Tümü
                    </Text>
                  </Button>
                  {allTags.map(tag => (
                    <Button
                      key={tag}
                      size='sm'
                      color={selectedTag === tag ? 'primary' : 'outlineBlack'}
                      onClick={() => handleTagClick(tag)}
                      className='w-auto border-platinum-stroke'>
                      <Text as='p' size='md'>
                        {tag}
                      </Text>
                    </Button>
                  ))}
                </GridFlexContainer>
              </div>
            </Appear>
            {/* Left Side: News Grid */}
            <Appear className='md:w-3/4 w-full'>
              {/* GlobalSearch'te kategoriler ve sluglar gösterilecek */}
              <GlobalSearchProvider links={searchLinks}>
                <GlobalSearchTrigger
                  className='w-full h-32 rounded-xl mb-10'
                  placeholder='Haber başlığı veya kategori ara'
                />
              </GlobalSearchProvider>
              <Appear className='h-full w-full' late>
                <GridFlexContainer layout='grid' columns={3} gap='lg'>
                  {filteredBlogs.map(blog => (
                    <Card
                      key={blog.id}
                      tags={blog.tags}
                      type='primary'
                      link={`/blogs/${blog.slug}`}
                      title={blog.title}
                      description={blog.description}
                      image={blog.image}
                      date={blog.date}
                      writer={blog.writer}
                    />
                  ))}
                </GridFlexContainer>
              </Appear>
            </Appear>
          </GridFlexContainer>
        </div>
      </div>
    </section>
  )
}
