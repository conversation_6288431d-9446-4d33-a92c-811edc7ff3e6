'use server'

import { getToken } from './base'

export const request = async (path: string, init: RequestInit | undefined = {}): Promise<Response | null> => {
  const token = await getToken()

  if (!token) {
    return null
  }

  return await fetch(`http://0.0.0.0:3003${path}`, {
    ...init,
    headers: {
      ...init.headers,
      // biome-ignore lint/style/useNamingConvention: Redundant
      Authorization: `Bearer ${token}`,
    },
  })
}
