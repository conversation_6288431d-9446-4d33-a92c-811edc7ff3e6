'use server'

import { headers, cookies as headersCookies } from 'next/headers'

export const getToken = async () => {
  const cookies = await headersCookies()

  const token = cookies.get('ozaco-token')?.value

  if (!token) {
    return null
  }

  return token
}

export const getLang = async () => {
  const cookies = await headersCookies()

  const customLang = cookies.get('ozaco-lang')?.value

  const headersList = await headers()
  const defaultLocale = headersList.get('accept-language')?.split(',')[0]?.split('-')[0]?.toLowerCase()
  const lang = customLang || defaultLocale || 'en'

  if (lang !== customLang) {
    cookies.set('ozaco-lang', lang)
  }

  return lang
}
