{"dependencies": {"@radix-ui/react-accordion": "^1.2.12", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-tooltip": "1.2.8", "@radix-ui/react-checkbox": "^1.3.3", "arktype": "^2.1.20", "react-intersection-observer": "^9.16.0", "react-markdown": "^10.1.0", "rehype-highlight": "^7.0.2", "rehype-raw": "^7.0.0", "rehype-sanitize": "^6.0.0", "remark-gfm": "^4.0.1"}, "devDependencies": {"@venture-vibe/icons": "workspace:*", "@venture-vibe/utils": "workspace:*", "@ozaco/cli": "^0.0.14", "@types/react": "^19.1.12", "motion": "^12.23.12", "react": "^19.1.1", "typescript": "^5.9.2", "@legendapp/state": "^3.0.0-beta.32", "@radix-ui/react-tabs": "^1.1.13"}, "exports": {"./shared": {"default": "./dist/shared.js", "source": "./src/shared/index.ts", "types": "./dist/shared.d.ts"}}, "tsx-exports": ["shared"], "files": ["dist"], "name": "@venture-vibe/components", "peerDependencies": {"@venture-vibe/icons": "workspace:*", "@venture-vibe/utils": "workspace:*", "motion": ">= 12.23.12", "react": ">= 19.1.1", "typescript": ">= 5.9.2", "@legendapp/state": ">= 3.0.0-beta.31"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "private": true, "type": "module", "version": "0.0.0"}