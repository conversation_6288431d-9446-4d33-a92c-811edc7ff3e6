import { clsx } from '@venture-vibe/utils'
import type { FC } from 'react'

/** biome-ignore lint/nursery/noUnresolvedImports: Package's fault */
import ReactMarkdown from 'react-markdown'
import rehypeHighlight from 'rehype-highlight'
import rehypeRaw from 'rehype-raw'
import rehypeSanitize from 'rehype-sanitize'
import remarkGfm from 'remark-gfm'

export interface MarkdownProps {
  source: string
  className?: string
}

export const Markdown: FC<MarkdownProps> = ({ source, className }) => {
  return (
    <div className={clsx('prose', className)}>
      <ReactMarkdown remarkPlugins={[remarkGfm]} rehypePlugins={[rehypeRaw, rehypeSanitize, rehypeHighlight]}>
        {source}
      </ReactMarkdown>
    </div>
  )
}
