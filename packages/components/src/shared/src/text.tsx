import { type BlobType, cva, type VariantProps } from '@venture-vibe/utils'
import type { FC, HTMLAttributes, ReactNode, Ref } from 'react'

export const createTextVariants = cva([], {
  variants: {
    size: {
      xs: 'text-xs',
      sm: 'text-sm',
      md: 'text-md',
      lg: 'text-lg',
      xl: 'text-xl',
      '2xl': 'text-2xl',
      '3xl': 'text-3xl',
    },
    thickness: {
      thin: 'font-thin',
      extralight: 'font-extralight',
      light: 'font-light',
      normal: 'font-normal',
      medium: 'font-medium',
      semibold: 'font-semibold',
      bold: 'font-bold',
      extrabold: 'font-extrabold',
      black: 'font-black',
    },
    decoration: {
      none: 'no-underline',
      underline: 'underline',
      lineThrough: 'line-through',
    },

    color: {
      white: 'text-white',
      black: 'text-black',
    },
  },
  defaultVariants: {
    size: 'md',
    thickness: 'normal',
    decoration: 'none',
  },
})

export type TextProps = (
  | ({
      as: 'p'
      ref?: Ref<HTMLParagraphElement>
    } & HTMLAttributes<HTMLParagraphElement>)
  | ({
      as: 'span'
      ref?: Ref<HTMLSpanElement>
    } & HTMLAttributes<HTMLSpanElement>)
  | ({
      as: 'label'
      ref?: Ref<HTMLLabelElement>
    } & HTMLAttributes<HTMLLabelElement>)
  | {
      as?: undefined
    }
) &
  VariantProps<typeof createTextVariants> & { children?: ReactNode; className?: string }

export const Text: FC<TextProps> = ({ as = 'p', children, preset, className, ...rest }): ReactNode => {
  const El = as as BlobType
  const styles = createTextVariants({
    preset,
    className,
    ...rest,
  })

  return (
    <El className={styles} {...rest}>
      {children}
    </El>
  )
}
