/** biome-ignore-all lint/nursery/useSortedClasses: Reduntant */
'use client'

import {
  ChevronDownIcon,
  HamburgerIcon,
  InstagramIcon,
  LinkedinIcon,
  TiktokIcon,
  YoutubeIcon,
} from '@venture-vibe/icons'
import { clsx, cva, type VariantProps } from '@venture-vibe/utils'
import { type FC, type ReactNode, useEffect, useState } from 'react'
import { useHero } from './hero'
import { Link } from './link'
import { SocialIcon } from './social-icon'
import { Text } from './text'

const SCROLL_THRESHOLD = 50

const createListItemVariants = cva(
  [
    'text-dark-gray',
    'hover:text-dark-spring',
    'leading-12',
    'transition-all',
    'duration-300',
    'ease-in-out',
    'select-none',
  ],
  {
    variants: {},
  },
)

export const createNavigationBarVariants = cva(
  [
    'z-50',
    'fixed',
    'inset-x-0',
    'mx-auto',
    'mb-0',
    'min-w-max',
    'min-h-max',
    'transition-transform',
    'duration-500',
    'ease-[cubic-bezier(0.12,0.23,0.5,1)]',
    '-translate-y-[150%]',
    'font-display',
    'desktop:max-w-[82.5rem]',
    'desktop:top-12',
    'tablet:max-w-[60rem]',
    'tablet:top-11',
    'max-tablet:top-10',
  ],
  {
    variants: {},
  },
)

export interface NavigationBarLink {
  text: string
  href?: string
  children?: NavigationBarLink[]
}

export interface NavigationbarCustomProps {
  logo: ReactNode
  links: NavigationBarLink[]
}

// biome-ignore lint/nursery/useExportsLast: Reduntant
export type NavigationBarProps = VariantProps<typeof createNavigationBarVariants> & {
  children?: ReactNode
  className?: string
} & NavigationbarCustomProps

const wrapLink = (link: NavigationBarLink) => {
  const key = link.href || link.text
  const listItemStyle = createListItemVariants({})

  const text = (
    <Text key={key} size='lg' className={listItemStyle}>
      {link.text}
    </Text>
  )

  const anchor = link.href ? (
    <Link key={key} href={link.href}>
      {text}
    </Link>
  ) : (
    text
  )

  const item = <li key={key}>{anchor}</li>

  return item
}

const createDropdownMenuItem = (link: NavigationBarLink) => {
  const listItems = link.children?.map(wrapLink) ?? []
  const chevronDown = <ChevronDownIcon viewBox='0 0 24 24' className='w-full h-full' />
  const listItemStyle = createListItemVariants({})

  const triggerWrapper = (
    <div className='flex flex-row items-center gap-[0.325rem] cursor-pointer'>
      <Text size='lg' className={clsx(listItemStyle, 'group-hover:text-dark-spring')}>
        {link.text}
      </Text>
      <div
        className={clsx([
          'w-[20px]',
          'h-[23px]',
          'items-center',
          'transition-transform',
          'duration-200',
          'ease-in-out',
          'group-hover:-rotate-180',
          'text-dark-gray',
          'group-hover:text-dark-spring',
        ])}>
        {chevronDown}
      </div>
    </div>
  )

  const listWrap = (
    <div
      className={clsx([
        'absolute',
        'z-100',
        'top-full',

        'w-max',
        'bg-white',
        'rounded-2xl',
        'p-10',
        'text-black',
        'shadow-[0px_4px_20px_0px_rgba(0,0,0,0.15)]',
        'opacity-0',
        'scale-95',
        '-translate-y-2',
        'pointer-events-none',
        'transition-all',
        'duration-300',
        'ease-in-out',
        'group-hover:opacity-100',
        'group-hover:scale-100',
        'group-hover:translate-y-0',
        'group-hover:pointer-events-auto',
      ])}>
      <ul className='flex flex-col gap-[0.75rem]'>{listItems}</ul>
    </div>
  )

  return (
    <li key={link.text} className={clsx('group', 'relative', 'py-6')}>
      {triggerWrapper}
      {listWrap}
    </li>
  )
}

export const NavigationBar: FC<NavigationBarProps> = props => {
  const { links, logo } = props

  const { hasShadow } = useHero()

  const [lastY, setLastY] = useState(0)
  const [scrollDirection, setScrollDirection] = useState(-1)
  const [directionTotal, setDirectionTotal] = useState(0)

  const [show, setShow] = useState(true)
  const [isMenuOpened, setIsMenuOpened] = useState(false)

  useEffect(() => {
    const onScroll = () => {
      const y = window.scrollY
      const gap = lastY - y
      const direction = gap / Math.abs(gap)

      setLastY(y)

      if (direction === scrollDirection) {
        setDirectionTotal(directionTotal + gap)
      } else {
        setScrollDirection(direction)
        setDirectionTotal(0)
      }

      if (Math.abs(directionTotal) < SCROLL_THRESHOLD) {
        return
      }

      setIsMenuOpened(false)

      if (direction === 1) {
        return setShow(true)
      }

      if (y > SCROLL_THRESHOLD) {
        setShow(false)
      }
    }

    window.addEventListener('scroll', onScroll)
    return () => window.removeEventListener('scroll', onScroll)
  }, [lastY, scrollDirection, directionTotal])

  const desktopNavItems = links.map(link => {
    if (link.children && link.children.length > 0) {
      return createDropdownMenuItem(link)
    }
    return wrapLink(link)
  })

  const allNavigableLinks = links.reduce((acc: NavigationBarLink[], link) => {
    if (link.children) {
      acc.push(...link.children)
    } else {
      acc.push(link)
    }
    return acc
  }, [])
  const mobileLinkListItems = allNavigableLinks.map(wrapLink)

  const linkList = (
    <ul className='flex flex-1 justify-center flex-row items-center gap-[3rem] max-desktop:hidden'>
      {desktopNavItems}
    </ul>
  )

  const hamburgerIcon = (
    <HamburgerIcon
      width={32}
      height={28}
      strokeWidth={8}
      onClick={() => setIsMenuOpened(!isMenuOpened)}
      isOpened={isMenuOpened}
      className='cursor-pointer'
    />
  )

  const hamburgerIconContainer = (
    <div className='flex w-[50px] h-[50px] justify-center items-center desktop:hidden'>{hamburgerIcon}</div>
  )

  const hamburgerMenuDropDown = (
    <div
      className={clsx(
        [
          'absolute',
          'w-full',
          'mt-2.5',
          'p-10',
          'bg-white',
          'rounded-xl',
          'transition-all',
          'duration-500',
          'ease-out',
          'opacity-0',
        ],
        {
          'max-desktop:opacity-100': isMenuOpened,
          'pointer-events-none': !isMenuOpened,
        },
      )}>
      <ul className='flex flex-col gap-11 mt-2'>{mobileLinkListItems}</ul>
    </div>
  )

  const navbarButtonContainer = (
    <div className='flex flex-row justify-center items-center gap-4'>
      <div className='flex-row gap-4 bg-dark p-4 hidden sm:flex ml-40 rounded-max'>
        {/* <SocialIcon color='secondary' icon={<FacebookIcon />} href='https://www.facebook.com/venturevibe' />
        <SocialIcon color='secondary' icon={<XIcon />} href='https://www.venturevibe.com/' /> */}
        <SocialIcon color='secondary' icon={<InstagramIcon />} href='https://www.instagram.com/helloventurevibe/' />
        <SocialIcon color='secondary' icon={<YoutubeIcon />} href='https://www.youtube.com/@HelloVentureVibe' />
        <SocialIcon color='secondary' icon={<TiktokIcon />} href='https://www.tiktok.com/@helloventurevibe' />
        <SocialIcon
          color='secondary'
          icon={<LinkedinIcon />}
          href='https://www.linkedin.com/company/the-venture-vibe/'
        />
      </div>
      {hamburgerIconContainer}
    </div>
  )

  const navbar = (
    <nav
      className={clsx(
        [
          'flex',
          'justify-between',
          'items-center',
          'bg-white',
          'rounded-[5rem]',
          'desktop:h-[5rem]',
          'desktop:p-8',
          'desktop:pl-12',
          'tablet:h-[4.6875rem]',
          'tablet:py-6',
          'tablet:px-8',
          'max-tablet:h-[4.4375rem]',
          'max-tablet:py-5',
          'max-tablet:px-6',
        ],
        {
          'desktop:shadow-[0_8px_30px_#7cae0e33]': hasShadow,
        },
      )}>
      <Link href='/'>{logo}</Link>
      {linkList}
      {navbarButtonContainer}
    </nav>
  )

  const header = (
    <header className='relative'>
      {navbar}
      {hamburgerMenuDropDown}
    </header>
  )

  return (
    <div
      className={clsx(createNavigationBarVariants(props), {
        'max-desktop:translate-y-0': true,
        'translate-y-0': show,
        'px-10': true,
      })}
      {...props}>
      {header}
    </div>
  )
}
